<?php
namespace App\Controller;

use App\Entity\Document;
use App\Entity\ReleasedPackage;
use App\Entity\Visa;
use App\Entity\Commentaire;
use App\Form\DocumentType;
use App\Entity\User;
use App\Repository\DocumentRepository;
use App\Service\WorkflowSimulator;
use App\Service\WorkflowTeleportation;
use App\Service\DocumentDuplicator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Workflow\Registry;
use Symfony\Component\HttpFoundation\JsonResponse;
use Knp\Component\Pager\PaginatorInterface;

#[Route('/document')]
final class DocumentController extends AbstractController
{

    private WorkflowSimulator $workflowSimulator;
    private WorkflowTeleportation $workflowTeleportation;
    private DocumentDuplicator $documentDuplicator;

    public function __construct(
        private Registry $registry,
        DocumentDuplicator $documentDuplicator,
        WorkflowSimulator $workflowSimulator,
        WorkflowTeleportation $workflowTeleportation
    ) {
        $this->workflowSimulator = $workflowSimulator;
        $this->workflowTeleportation = $workflowTeleportation;
        $this->documentDuplicator = $documentDuplicator;
    }

    #[Route(name: 'app_document_index', methods: ['GET'])]
    public function index(DocumentRepository $documentRepository, EntityManagerInterface $entityManager): Response
    {
        $document = $documentRepository->find(1);
        $workflow = $this->registry->get($document,'document_workflow');
        $marking = [];
        $marking_start = $workflow->getMarking($document)->getPlaces();
        foreach ($workflow->getEnabledTransitions($document) as $transition) {
            $marking[$transition->getTos()[0]] = 1;
            $from = $transition->getFroms()[0];
            if (isset($marking_start[$from])) {
                unset($marking_start[$from]);
            }
        }
        $marking = array_merge($marking, $marking_start);
        $document->setCurrentSteps($marking);
        $entityManager->flush();
        return $this->render('document/index.html.twig', [
            'documents' => $documentRepository->findAll(),
        ]);
    }

    #[Route('/suivie-ref', name: 'suivie_ref', methods: ['GET'])]
    public function suivie_ref(
        Request $request,
        DocumentRepository $documentRepository,
        EntityManagerInterface $entityManager,
        PaginatorInterface $paginator,
        WorkflowSimulator $workflowSimulator
    ): Response {
        $queryBuilder = $documentRepository->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'p');

        // Apply filters
        $filters = [
            'search' => $request->query->get('search'),
            'reference' => $request->query->get('reference'),
            'docType' => $request->query->get('docType'),
            'procType' => $request->query->get('procType'),
            'activity' => $request->query->get('activity'),
            'material' => $request->query->get('material'),
            'productCode' => $request->query->get('productCode'),
            'currentStep' => $request->query->get('currentStep'),
        ];

        // Global search across multiple fields
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('d.reference', ':search'),
                    $queryBuilder->expr()->like('d.refTitleFra', ':search'),
                    $queryBuilder->expr()->like('d.prodDraw', ':search'),
                    $queryBuilder->expr()->like('d.material', ':search'),
                    $queryBuilder->expr()->like('d.productCode', ':search'),
                    $queryBuilder->expr()->like('p.Activity', ':search')
                )
            )->setParameter('search', $searchTerm);
        }

        // Specific field filters
        if (!empty($filters['reference'])) {
            $queryBuilder->andWhere('d.reference LIKE :reference')
                ->setParameter('reference', '%' . $filters['reference'] . '%');
        }

        if (!empty($filters['docType'])) {
            $queryBuilder->andWhere('d.docType = :docType')
                ->setParameter('docType', $filters['docType']);
        }

        if (!empty($filters['procType'])) {
            $queryBuilder->andWhere('d.procType = :procType')
                ->setParameter('procType', $filters['procType']);
        }

        if (!empty($filters['activity'])) {
            $queryBuilder->andWhere('p.Activity LIKE :activity')
                ->setParameter('activity', '%' . $filters['activity'] . '%');
        }

        if (!empty($filters['material'])) {
            $queryBuilder->andWhere('d.material LIKE :material')
                ->setParameter('material', '%' . $filters['material'] . '%');
        }

        if (!empty($filters['productCode'])) {
            $queryBuilder->andWhere('d.productCode LIKE :productCode')
                ->setParameter('productCode', '%' . $filters['productCode'] . '%');
        }

        // Current step filtering (JSON field search using LIKE)
        if (!empty($filters['currentStep'])) {
            $queryBuilder->andWhere('d.currentSteps LIKE :currentStep')
                ->setParameter('currentStep', '%"' . $filters['currentStep'] . '"%');
        }

        // Order by most recent first
        $queryBuilder->orderBy('d.id', 'DESC');

        $pagination = $paginator->paginate(
            $queryBuilder,
            $request->query->getInt('page', 1),
            20 // Increased page size for better user experience
        );

        $documents = $pagination->getItems();
        $simulated = $workflowSimulator->simulateWorkflowList($documents);

        // Suppression du refresh inutile qui ralentit les performances
        // foreach ($documents as $document) {
        //     $entityManager->refresh($document);
        // }

        // Get filter options for dropdowns
        $filterOptions = $this->getFilterOptions($documentRepository);

        return $this->render('document/suivie_list.html.twig', [
            'results' => $simulated,
            'pagination' => $pagination,
            'filters' => $filters,
            'filterOptions' => $filterOptions,
        ]);
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions(DocumentRepository $documentRepository): array
    {
        $queryBuilder = $documentRepository->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'p');

        // Get distinct document types
        $docTypes = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT d.docType')
            ->where('d.docType IS NOT NULL')
            ->orderBy('d.docType', 'ASC')
            ->getQuery()
            ->getScalarResult();

        // Get distinct process types
        $procTypes = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT d.procType')
            ->where('d.procType IS NOT NULL')
            ->orderBy('d.procType', 'ASC')
            ->getQuery()
            ->getScalarResult();

        // Get distinct activities
        $activities = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT p.Activity')
            ->leftJoin('d.relPack', 'p')
            ->where('p.Activity IS NOT NULL')
            ->orderBy('p.Activity', 'ASC')
            ->getQuery()
            ->getScalarResult();

        // Get distinct materials
        $materials = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT d.material')
            ->where('d.material IS NOT NULL')
            ->orderBy('d.material', 'ASC')
            ->setMaxResults(50) // Limit for performance
            ->getQuery()
            ->getScalarResult();

        // Get all workflow steps from workflow.yaml
        $commonSteps = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        return [
            'docTypes' => array_column($docTypes, 'docType'),
            'procTypes' => array_column($procTypes, 'procType'),
            'activities' => array_column($activities, 'Activity'),
            'materials' => array_column($materials, 'material'),
            'currentSteps' => $commonSteps,
        ];
    }

    #[Route('/suivie-ref/export', name: 'suivie_ref_export', methods: ['GET'])]
    public function suivieRefExport(
        Request $request,
        DocumentRepository $documentRepository,
        EntityManagerInterface $entityManager,
        WorkflowSimulator $workflowSimulator
    ): Response {
        $queryBuilder = $documentRepository->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'p');

        // Apply the same filters as the main page
        $filters = [
            'search' => $request->query->get('search'),
            'reference' => $request->query->get('reference'),
            'docType' => $request->query->get('docType'),
            'procType' => $request->query->get('procType'),
            'activity' => $request->query->get('activity'),
            'material' => $request->query->get('material'),
            'productCode' => $request->query->get('productCode'),
            'currentStep' => $request->query->get('currentStep'),
        ];

        // Apply filters (same logic as main method)
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('d.reference', ':search'),
                    $queryBuilder->expr()->like('d.refTitleFra', ':search'),
                    $queryBuilder->expr()->like('d.prodDraw', ':search'),
                    $queryBuilder->expr()->like('d.material', ':search'),
                    $queryBuilder->expr()->like('d.productCode', ':search'),
                    $queryBuilder->expr()->like('p.Activity', ':search')
                )
            )->setParameter('search', $searchTerm);
        }

        if (!empty($filters['reference'])) {
            $queryBuilder->andWhere('d.reference LIKE :reference')
                ->setParameter('reference', '%' . $filters['reference'] . '%');
        }

        if (!empty($filters['docType'])) {
            $queryBuilder->andWhere('d.docType = :docType')
                ->setParameter('docType', $filters['docType']);
        }

        if (!empty($filters['procType'])) {
            $queryBuilder->andWhere('d.procType = :procType')
                ->setParameter('procType', $filters['procType']);
        }

        if (!empty($filters['activity'])) {
            $queryBuilder->andWhere('p.Activity LIKE :activity')
                ->setParameter('activity', '%' . $filters['activity'] . '%');
        }

        if (!empty($filters['material'])) {
            $queryBuilder->andWhere('d.material LIKE :material')
                ->setParameter('material', '%' . $filters['material'] . '%');
        }

        if (!empty($filters['productCode'])) {
            $queryBuilder->andWhere('d.productCode LIKE :productCode')
                ->setParameter('productCode', '%' . $filters['productCode'] . '%');
        }

        if (!empty($filters['currentStep'])) {
            $queryBuilder->andWhere('d.currentSteps LIKE :currentStep')
                ->setParameter('currentStep', '%"' . $filters['currentStep'] . '"%');
        }

        $queryBuilder->orderBy('d.id', 'DESC');
        $documents = $queryBuilder->getQuery()->getResult();

        // Generate CSV
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="suivi_documents_' . date('Y-m-d_H-i-s') . '.csv"');

        $csvData = "Pack;Activity;Reference;RefRev;ProdDraw;ProdDrawRev;DocType;ProcType;Material;ProductCode;CurrentSteps\n";

        foreach ($documents as $document) {
            $currentSteps = implode('|', array_keys($document->getCurrentSteps()));
            $csvData .= sprintf(
                "%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
                $document->getRelPack() ? $document->getRelPack()->getId() : '',
                $document->getRelPack() ? $document->getRelPack()->getActivity() : '',
                $document->getReference() ?? '',
                $document->getRefRev() ?? '',
                $document->getProdDraw() ?? '',
                $document->getProdDrawRev() ?? '',
                $document->getDocType() ?? '',
                $document->getProcType() ?? '',
                $document->getMaterial() ?? '',
                $document->getProductCode() ?? '',
                $currentSteps
            );
        }

        $response->setContent($csvData);
        return $response;
    }

    #[Route('/backward/{id}', name: 'backward', methods: ['GET'])]
    public function backward(Document $document, EntityManagerInterface $entityManager): JsonResponse
    {
        $TP_List = $this->workflowTeleportation->getDocumentTeleportation($document);
        return new JsonResponse($TP_List);
    }

    #[Route('/retour', name: 'retour', methods: ['GET'])]
    public function retour(EntityManagerInterface $entityManager, DocumentRepository $Repository): Response
    {
        // Use optimized query with limit and ordering for better performance
        $packages = $entityManager->getRepository(ReleasedPackage::class)
            ->createQueryBuilder('p')
            ->orderBy('p.id', 'DESC')
            ->setMaxResults(1000) // Limit for performance
            ->getQuery()
            ->getResult();

        return $this->render('document/retour.html.twig', [
            'packages' => $packages,
        ]);
    }


    #[Route('/review/{place}', name: 'app_document_place', methods: ['GET'])]
    public function place(DocumentRepository $documentRepository, string $place, EntityManagerInterface $entityManager): Response
    {
        // Cache des documents par place pour éviter les requêtes répétées
        static $documentsCache = [];
        static $cacheTime = [];

        $now = time();
        $cacheKey = $place;

        if ($place === 'BE_0' || $place === 'BE_1' || $place === 'BE') {
            return $this->redirectToRoute('app_package');
        }

        // Vérifier si on a un cache valide (1 minute)
        if (!isset($documentsCache[$cacheKey]) || !isset($cacheTime[$cacheKey]) || ($now - $cacheTime[$cacheKey]) > 60) {
            if ($place === 'Qual_Logistique' || $place === 'Logistique') {
                // Utiliser la méthode optimisée pour les documents logistiques
                $documents = $documentRepository->findActiveDocumentsInLogisticsSteps();
                // On considère 'Qual_Logistique' comme nom d'affichage (ou de template)
                $place = 'Qual_Logistique';
            } else {
                // Utiliser la méthode optimisée pour les autres étapes
                $documents = $documentRepository->findActiveDocumentsInStep($place);
            }

            $documentsCache[$cacheKey] = $documents;
            $cacheTime[$cacheKey] = $now;
        } else {
            $documents = $documentsCache[$cacheKey];
        }

        // Tri en fonction du nombre de jours passés dans l'état $place
        usort($documents, function($a, $b) use ($place) {
            return $b->getDaysInState($place) <=> $a->getDaysInState($place);
        });

        return $this->render('document/places/document_' . $place . '.html.twig', [
            'documents' => $documents,
            'place'     => $place,
            'total_documents' => count($documents),
        ]);
    }

    #[Route('/document/{id}/visas', name: 'document_visas', methods: ['GET'])]
    public function getVisas(Document $document): Response
    {
        // On récupère tous les visas du document
        $visas = $document->getVisas();

        // On convertit en tableau
        $data = [];
        foreach ($visas as $visa) {
            $data[] = [
                'id'       => $visa->getId(),
                'name'     => $visa->getName(),
                'status'   => $visa->getStatus(),
                'dateVisa' => $visa->getDateVisa()?->format('d/m/Y H:i'),
                'signer'   => (string)$visa->getValidator(),
            ];
        }
        return $this->json($data);
    }


    #[Route('/update-document', name: 'update_document', methods: ['POST'])]
    public function updateDocument(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $places_docType = [
            'MACH' => ['ORIGINE'=> 'Machining', 'DESTINATION'=> 'Machining'],
            'MOLD' => ['ORIGINE'=> 'Molding', 'DESTINATION'=> 'Molding'],
            'ASSY' => ['ORIGINE'=> 'Assembly', 'DESTINATION'=> 'Assembly'],
            'PUR'  => ['ORIGINE'=> ['Achat_F30','Achat_RFQ','Quality'], 'DESTINATION'=> 'Quality'],
        ];

        // Récupérer les données de la requête AJAX
        $data = json_decode($request->getContent(), true);

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'], $data['field'], $data['value'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];
        $field      = $data['field'];
        $value      = $data['value'];

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // On récupère l'ancien docType pour la gestion du workflow après mise à jour
        $oldDocType = $document->getDoctype();

        // Exemple : si on veut gérer spécifiquement un champ "superviseur"
        if ($field === 'superviseur') {
            // dd($documentId,$value);
            $user = $entityManager->getRepository(User::class)->find($value);
            if (!$user) {
                return new JsonResponse(['status' => 'error', 'message' => 'Utilisateur non trouvé'], 404);
            }
            $document->setSuperviseur($user);
            $entityManager->persist($document);
            $entityManager->flush();

        } else if($field === 'qInspection') {
            if (!is_array($value)) {
                return new JsonResponse(['status' => 'error', 'message' => 'La valeur pour qInspection doit être une liste.'], 400);
            }
            $document->setQInspection($value);
        } else if($field === 'qDocRec'){
            if (!is_array($value)) {
                return new JsonResponse(['status' => 'error', 'message' => 'La valeur pour qDocRec doit être une liste.'], 400);
            }
            $document->setQDocRec($value);
        } else if ($field === 'Material_Type') {
            $document->setMaterialType($value);
            $entityManager->persist($document);
            $entityManager->flush();
        }

        else {
            // Sinon on continue avec la méthode "set + ucfirst($field)" générique
            $setterMethod = 'set'.ucfirst($field);
            if (method_exists($document, $setterMethod)) {
                $document->$setterMethod($value);
            } else {
                return new JsonResponse(['status' => 'error', 'message' => 'Méthode introuvable pour le champ '.$field], 500);
            }
        }

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Modification du champ ' . $field);

        // On persiste et flush
        $entityManager->persist($document);
        $entityManager->flush();

        // Exemple : ta logique de workflow si on modifie le docType
        if ($field === 'doctype' && $value !== "DOC") {
            $newDocType = $value;
            $ORIGINE    = $places_docType[$oldDocType]['ORIGINE'];
            $DESTINATION= $places_docType[$newDocType]['DESTINATION'];

            // Si tu utilises un WorkflowRegistry, il faut injecter $this->registry ou autre
            $workflow = $this->registry->get($document, 'document_workflow');
            $markingStart = $workflow->getMarking($document)->getPlaces();

            if (is_array($ORIGINE)) {
                if (array_key_exists('Achat_F30', $markingStart)) {
                    $ORIGINE = 'Achat_F30';
                } elseif (array_key_exists('Achat_RFQ', $markingStart)) {
                    $ORIGINE = 'Achat_RFQ';
                } else {
                    $ORIGINE = 'Quality';
                }
            }
            unset($markingStart[$ORIGINE]);
            $markingStart[$DESTINATION] = 1;

            if (in_array($ORIGINE, ['Quality','Achat_F30','Achat_RFQ'])) {
                $visas = $document->getVisas();
                foreach ($visas as $visa) {
                    if (in_array($visa->getName(), ['visa_Quality', 'visa_Achat_F30', 'visa_Achat_RFQ'])) {
                        $entityManager->remove($visa);
                    }
                }
            }
            $document->setCurrentSteps($markingStart);

            // Ajouter une entrée dans l'historique des mises à jour
            $document->addUpdate('edit', $this->getUser(), 'Mise à jour du document');

            $entityManager->flush();
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Document mis à jour avec succès']);
    }

    // update metroControl field
    #[Route('/update-metro-control', name: 'update_metro_control', methods: ['POST'])]
    public function UpdateMetroControl(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        // Récupérer les données de la requête AJAX
        $data = json_decode($request->getContent(), true);

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'], $data['metroControl'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];
        $metroControl = $data['metroControl'];
        // dd($metroControl);

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // Mettre à jour le champ metroControl
        $document->setMetroControl($metroControl);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Modification du contrôle métrologique');

        // Persister les modifications
        $entityManager->persist($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'MetroControl mis à jour avec succès']);
    }
    // public function workflowMove(Document $document, EntityManagerInterface $entityManager): JsonResponse
    // {
    //     $workflow = $this->registry->get($document, 'document_workflow');
    //     $markingStart = $workflow->getMarking($document)->getPlaces();
    //     $enabledTransitions = $workflow->getEnabledTransitions($document);
    //     $marking = [];
    //     $timestamps = $document->getStateTimestamps() ?? [];

    //     foreach ($enabledTransitions as $transition) {
    //         // On marque la place « to » comme atteignable
    //         $to = $transition->getTos()[0];
    //         $marking[$to] = 1;

    //         if (!isset($timestamps[$to])) {
    //             $timestamps[$to] = (new \DateTime())->format('Y-m-d H:i:s');
    //         }
    //         // On supprime la place « from » de la liste des places courantes
    //         // si elle y est encore présente
    //         $from = $transition->getFroms()[0];
    //         if (isset($markingStart[$from])) {
    //             unset($markingStart[$from]);
    //         }
    //     }

    //     // Fusion : on conserve aussi les places non « consommées »
    //     $finalMarking = array_merge($marking, $markingStart);

    //     // $timestamps = array_intersect_key($timestamps, $finalMarking);

    //     // Mise à jour de l’entité Document (ou autre entité ciblée)
    //     $document->setCurrentSteps($finalMarking);
    //     $document->setStateTimestamps($timestamps);

    //     // Persistance en base
    //     $entityManager->flush();

    //     return new JsonResponse(['status' => 'success', 'message' => $finalMarking]);
    // }
    public function workflowMove(Document $document, EntityManagerInterface $entityManager): JsonResponse
    {
        $workflow = $this->registry->get($document, 'document_workflow');
        $markingStart = $workflow->getMarking($document)->getPlaces();
        $enabledTransitions = $workflow->getEnabledTransitions($document);
        $marking = [];

        foreach ($enabledTransitions as $transition) {
            // On marque la place « to » comme atteignable
            $to = $transition->getTos()[0];
            $marking[$to] = 1;

            // Enregistrer l'entrée dans le nouvel état
            $document->addStateEnter($to);

            // On supprime la place « from » de la liste des places courantes
            // si elle y est encore présente
            $from = $transition->getFroms()[0];
            if (isset($markingStart[$from])) {
                // Enregistrer la sortie de l'état précédent
                $document->addStateExit($from);
                unset($markingStart[$from]);
            }

            // Si le document arrive dans la place Assembly, on modifie son procType à E et son Unit à PC
            if ($to === 'Assembly') {
                // Ne pas écraser les valeurs existantes si elles sont déjà définies
                $document->setProcType('E');
                $document->setUnit('PC');

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Assembly: procType=E, Unit=PC');

                // Conserver les autres champs s'ils sont déjà définis
                if ($document->getCustDrawing() === null) $document->setCustDrawing($document->getCustDrawing());
                if ($document->getCustDrawingRev() === null) $document->setCustDrawingRev($document->getCustDrawingRev());
                if ($document->getAction() === null) $document->setAction($document->getAction());
                if ($document->getEx() === null) $document->setEx($document->getEx());
            }

            // Si le document arrive dans la place Machining, on modifie son procType à E, son Unit à PC, son prodAgent à USI et son materialType à HALB
            if ($to === 'Machining') {
                $document->setProcType('E');
                $document->setUnit('PC');
                $document->setProdAgent('USI');
                $document->setMaterialType('HALB');
                $document->setMatProdType('HALB');

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Machining: procType=E, Unit=PC, prodAgent=USI, materialType=HALB, matProdType=HALB');

                // Conserver les autres champs s'ils sont déjà définis
                if ($document->getCustDrawing() === null) $document->setCustDrawing($document->getCustDrawing());
                if ($document->getCustDrawingRev() === null) $document->setCustDrawingRev($document->getCustDrawingRev());
                if ($document->getAction() === null) $document->setAction($document->getAction());
                if ($document->getEx() === null) $document->setEx($document->getEx());
            }

            // Si le document arrive dans la place Molding, on modifie son procType à E et son materialType à HALB
            if ($to === 'Molding') {
                $document->setProcType('E');
                $document->setMaterialType('HALB');
                $document->setMatProdType('HALB');

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Molding: procType=E, materialType=HALB, matProdType=HALB');

                // Conserver les autres champs s'ils sont déjà définis
                if ($document->getCustDrawing() === null) $document->setCustDrawing($document->getCustDrawing());
                if ($document->getCustDrawingRev() === null) $document->setCustDrawingRev($document->getCustDrawingRev());
                if ($document->getAction() === null) $document->setAction($document->getAction());
                if ($document->getEx() === null) $document->setEx($document->getEx());
            }
        }

        // Fusion : on conserve aussi les places non « consommées »
        $finalMarking = array_merge($marking, $markingStart);

        // Mise à jour de l'entité Document (ou autre entité ciblée)
        $document->setCurrentSteps($finalMarking);

        // Persistance en base
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => $finalMarking]);
    }

    // create visa
    #[Route('/create-visa', name: 'create_visa', methods: ['POST'])]
    public function createVisa(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        // Récupérer les données de la requête AJAX
        $data = json_decode($request->getContent(), true);

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['documentId'], $data['name'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['documentId'];
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        if(in_array($data['name'], ['Assembly','Machining','Molding'])){
            $data['name'] = "prod";
            $document->setProcType('E');

            // Si le document arrive dans la place Assembly, on modifie également son Unit à PC
            if ($data['name'] === 'Assembly') {
                $document->setUnit('PC');
                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Assembly: Unit=PC');
            }

            // Si le document arrive dans la place Machining, on modifie son Unit à PC, son prodAgent à USI et son materialType à HALB
            if ($data['name'] === 'Machining') {
                $document->setUnit('PC');
                $document->setProdAgent('USI');
                $document->setMatProdType('HALB');
                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Machining: Unit=PC, prodAgent=USI, matProdType=HALB');
            }

            // Si le document arrive dans la place Molding, on modifie son materialType à HALB
            if ($data['name'] === 'Molding') {
                $document->setMatProdType('HALB');
                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Molding: matProdType=HALB');
            }

            $entityManager->persist($document);
            $entityManager->flush();
        }

        $name = "visa_" . $data['name'];


        // Vérifier si le document a déjà un visa avec le même nom et en statut valid
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === $name && $existingVisa->getStatus() === 'valid') {
                return new JsonResponse(['status' => 'error', 'message' => 'visa valid existant'], 400);
            }
            if ($existingVisa->getName() === $name && $existingVisa->getStatus() !== 'valid') {
                $entityManager->remove($existingVisa);
                $entityManager->flush();
            }
        }

        $entityManager->refresh($document);
        // Créer l’entité Visa
        $this->creationVisa($document, $name, $entityManager);

        // refresh
        $entityManager->refresh($document);
        // Mettre à jour le workflow
        // $workflow = $this->registry->get($document, 'document_workflow');
        // dd($enabledTransitions = $workflow->getEnabledTransitions($document));
        $this->workflowMove($document, $entityManager);

        return new JsonResponse(['status' => 'success', 'message' => 'Visa créé avec succès']);
    }

    // create_visa_inventory

    #[Route('/create-visa-inventory', name: 'create_visa_inventory', methods: ['POST'])]
    public function createVisaInventory(
    Request $request, DocumentRepository $documentRepository, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $documentId = $data['documentId'];
        $document = $documentRepository->find($documentId);

        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        $hasQualLogistiqueVisa = false;
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === 'visa_Qual_Logistique' && $existingVisa->getStatus() === 'valid') {
                $hasQualLogistiqueVisa = true;
                break;
            }
        }

        // Vérifier si le document a déjà un visa avec le même nom et en statut valid
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === 'visa_Logistique' && $existingVisa->getStatus() === 'valid') {
                return new JsonResponse(['status' => 'error', 'message' => 'visa valid existant'], 400);
            }
            if ($existingVisa->getName() === 'visa_Logistique' && $existingVisa->getStatus() !== 'valid') {
                $entityManager->remove($existingVisa);
                $entityManager->flush();
            }
        }

        if (!$hasQualLogistiqueVisa) {
            $this->creationVisa($document, 'visa_Qual_Logistique', $entityManager);
            // refresh
            $entityManager->refresh($document);
            $this->workflowMove($document, $entityManager);

        } else {
            $this->creationVisa($document, 'visa_Logistique', $entityManager);
            // refresh
            $entityManager->refresh($document);
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa Inventory créé avec succès']);
    }

    public function creationVisa(Document $document, String $nameVisa, EntityManagerInterface $entityManager): void
    {
        $visa = new Visa();
        $visa->setReleasedDrawing($document);
        $visa->setName($nameVisa);
        $visa->setStatus('valid');
        $visa->setDateVisa(new \DateTimeImmutable());
        // current user
        $currentUser = $this->getUser();
        $visa->setValidator($currentUser);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('visa', $currentUser, 'Ajout du visa ' . $nameVisa);

        $entityManager->persist($visa);
        $entityManager->flush();
    }



    #[Route('/new', name: 'app_document_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $document = new Document();
        $form = $this->createForm(DocumentType::class, $document);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Ajouter une entrée dans l'historique des mises à jour
            $document->addUpdate('create', $this->getUser(), 'Création du document');

            $entityManager->persist($document);
            $entityManager->flush();

            return $this->redirectToRoute('app_document_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('document/new.html.twig', [
            'document' => $document,
            'form' => $form,
        ]);
    }

    // get all prisDans1 attribute of document in ajax - Optimized version
    #[Route('/prisDans1', name: 'get_prisDans1', methods: ['GET'])]
    public function prisDans1(DocumentRepository $documentRepository): JsonResponse
    {
        // Use optimized query to get distinct values directly from database
        $qb = $documentRepository->createQueryBuilder('d');
        $qb->select('DISTINCT d.prisDans1')
           ->where('d.prisDans1 IS NOT NULL')
           ->orderBy('d.prisDans1', 'ASC');

        $result = $qb->getQuery()->getScalarResult();
        $distinctPrisDans1Values = array_column($result, 'prisDans1');

        return new JsonResponse($distinctPrisDans1Values);
    }

        // get all prisDans1 attribute of document in ajax
        #[Route('/prisDans2', name: 'get_prisDans2', methods: ['GET'])]
        public function prisDans2(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.prisDans2')
               ->where('d.prisDans2 IS NOT NULL')
               ->orderBy('d.prisDans2', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctPrisDans2Values = array_column($result, 'prisDans2');

            return new JsonResponse($distinctPrisDans2Values);
        }

        #[Route('/purchasingGroup', name: 'get_purchasingGroup', methods: ['GET'])]
        public function purchasingGroup(DocumentRepository $documentRepository): JsonResponse
        {
            $purchasingGroupValues = $documentRepository->createQueryBuilder('d')
                ->select('DISTINCT d.purchasingGroup')
                ->where('d.purchasingGroup IS NOT NULL')
                ->orderBy('d.purchasingGroup', 'ASC')
                ->getQuery()
                ->getScalarResult();

            return new JsonResponse(array_column($purchasingGroupValues, 'purchasingGroup'));
        }

        #[Route('/commodityCode', name: 'get_commodityCode', methods: ['GET'])]
        public function commodityCode(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.commodityCode')
               ->where('d.commodityCode IS NOT NULL')
               ->orderBy('d.commodityCode', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctCommodityCodeValues = array_column($result, 'commodityCode');

            return new JsonResponse($distinctCommodityCodeValues);
        }

        #[Route('/hts', name: 'get_hts', methods: ['GET'])]
        public function hts(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.hts')
               ->where('d.hts IS NOT NULL')
               ->orderBy('d.hts', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctHtsValues = array_column($result, 'hts');

            return new JsonResponse($distinctHtsValues);
        }

        #[Route('/productCode', name: 'get_productCode', methods: ['GET'])]
        public function productCode(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.productCode')
               ->where('d.productCode IS NOT NULL')
               ->orderBy('d.productCode', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctProductCodeValues = array_column($result, 'productCode');

            return new JsonResponse($distinctProductCodeValues);
        }

        #[Route('/eccn', name: 'get_eccn', methods: ['GET'])]
        public function eccn(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.eccn')
               ->where('d.eccn IS NOT NULL')
               ->orderBy('d.eccn', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctEccnValues = array_column($result, 'eccn');

            return new JsonResponse($distinctEccnValues);
        }


    #[Route('/create-visa-be0/{id}', name: 'release_package', methods: ['POST'])]
    public function createVisaBE0(ReleasedPackage $package, EntityManagerInterface $entityManager, Request $request): JsonResponse
    {
        $documents = $package->getDocuments();
        $verif = $request->request->get('verif');

        $user = $entityManager->getRepository(User::class)->find($verif);
        $package->setVerif($user);
        foreach ($documents as $document) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName('visa_BE_0');
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($this->getUser());
            $entityManager->persist($visa);
            $entityManager->flush();
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa BE_0 créé avec succès']);
    }

    #[Route('/create-visa-be1/{id}', name: 'verification_package', methods: ['POST'])]
    public function createVisaBE1(ReleasedPackage $package, EntityManagerInterface $entityManager, Request $request): JsonResponse
    {
        $documents = $package->getDocuments();
        $valid = $request->request->get('valid');

        $user = $entityManager->getRepository(User::class)->find($valid);
        $package->setValid($user);
        foreach ($documents as $document) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName('visa_BE_1');
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($this->getUser());
            $entityManager->persist($visa);
            $entityManager->flush();
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa BE_1 créé avec succès']);
    }

    #[Route('/create-visa-be/{id}', name: 'validation_package', methods: ['POST'])]
    public function createVisaBE(ReleasedPackage $package, EntityManagerInterface $entityManager): JsonResponse
    {
        $documents = $package->getDocuments();
        foreach ($documents as $document) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName('visa_BE');
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($this->getUser());
            $entityManager->persist($visa);
            $entityManager->flush();
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa BE créé avec succès']);
    }

    #[Route('/refuser-package/{id}', name: 'refuser_package', methods: ['POST'])]
    public function refuserPackage(ReleasedPackage $package, EntityManagerInterface $entityManager): JsonResponse
    {
        $documents = $package->getDocuments();
        foreach ($documents as $document) {
            // Récupérer l'état actuel
            $workflow = $this->registry->get($document, 'document_workflow');
            $currentMarking = $workflow->getMarking($document)->getPlaces();

            // Pour chaque état actuel, enregistrer la sortie
            foreach (array_keys($currentMarking) as $currentState) {
                $document->addStateExit($currentState);
            }

            // Définir le nouvel état BE_0
            $marking = ['BE_0' => 1];
            $document->setCurrentSteps($marking);

            // Enregistrer l'entrée dans BE_0
            $document->addStateEnter('BE_0', $this->getUser());

            $entityManager->flush();

            // Supprimer les visas
            $visas = $entityManager->getRepository(Visa::class)->findBy(['releasedDrawing' => $document]);
            foreach ($visas as $visa) {
                $entityManager->remove($visa);
            }
        }

        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Package refusé avec succès']);
    }



    #[Route('/add-document/{id}', name: 'add_document', methods: ['POST'])]
    public function addDocument(Request $request, EntityManagerInterface $entityManager, $id): JsonResponse
    {
        $data = $request->request->all();

        $package = $entityManager->getRepository(ReleasedPackage::class)->find($id);
        $document = new Document();
        $document->setReference($data['reference'] ?? null);
        $document->setRefRev($data['refRev'] ?? null);
        $document->setIdAletiq($data['idAletiq'] ?? null);
        $document->setProdDraw($data['prodDraw'] ?? null);
        $document->setProdDrawRev($data['prodDrawRev'] ?? null);
        $document->setAlias($data['alias'] ?? null);
        $document->setRefTitleFra($data['refTitleFra'] ?? null);
        $document->setAction($data['action'] ?? null);
        $document->setDocType($data['docType'] ?? null);
        $document->setMaterialType($data['materialType'] ?? null);
        $document->setInventoryImpact($data['inventoryImpact'] ?? null);
        $document->setEx($data['ex'] ?? null);
        $document->setCustDrawing($data['custDrawing'] ?? null);
        $document->setCustDrawingRev($data['custDrawingRev'] ?? null);
        $document->setWeight($data['weight'] ?? null);
        $document->setWeightUnit($data['weightUnit'] ?? null);
        $document->setMaterial($data['material'] ?? null);
        $document->setPlatingSurface($data['platingSurface'] ?? null);
        $document->setPlatingSurfaceUnit($data['platingSurfaceUnit'] ?? null);
        $document->setInternalMachRec($data['internalMachRec'] ?? null);
        $document->setEccn($data['eccn'] ?? null);
        $document->setRdo($data['rdo'] ?? null);
        $document->setHts($data['hts'] ?? null);
        $document->setDocImpact(0);

        if (isset($data['comments'])) {
            $commentaire = new Commentaire();
            $commentaire->setUser($this->getUser());
            $commentaire->setCommentaire($data['comments']);
            $commentaire->settype('principal');
            $commentaire->setDocuments($document);
            $commentaire->setCreatedAt(new \DateTimeImmutable());
            $entityManager->persist($commentaire);
        }

        $document->setRelPack($package);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('create', $this->getUser(), 'Création du document');

        $entityManager->persist($document);
        $entityManager->flush();
        return new JsonResponse(['status' => 'success', 'message' => 'Document créé avec succès']);
    }


    #[Route('/maj-document/{id}', name: 'maj_document', methods: ['POST'])]
    public function majDocument(Request $request, EntityManagerInterface $entityManager, $id): JsonResponse
    {
        $data = $request->request->all();
        $document = $entityManager->getRepository(Document::class)->find($id);

        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        $document->setReference($data['reference'] ?? null);
        $document->setRefRev($data['refRev'] ?? null);
        $document->setIdAletiq($data['idAletiq'] ?? null);
        $document->setProdDraw($data['prodDraw'] ?? null);
        $document->setProdDrawRev($data['prodDrawRev'] ?? null);
        $document->setAlias($data['alias'] ?? null);
        $document->setRefTitleFra($data['refTitleFra'] ?? null);
        $document->setAction($data['action'] ?? null);
        $document->setDocType($data['docType'] ?? null);
        $document->setMaterialType($data['materialType'] ?? null);
        $document->setInventoryImpact($data['inventoryImpact'] ?? null);
        $document->setEx($data['ex'] ?? null);
        $document->setCustDrawing($data['custDrawing'] ?? null);
        $document->setCustDrawingRev($data['custDrawingRev'] ?? null);
        $document->setWeight($data['weight'] ?? null);
        $document->setWeightUnit($data['weightUnit'] ?? null);
        $document->setMaterial($data['material'] ?? null);
        $document->setPlatingSurface($data['platingSurface'] ?? null);
        $document->setPlatingSurfaceUnit($data['platingSurfaceUnit'] ?? null);
        $document->setInternalMachRec($data['internalMachRec'] ?? null);
        $document->setEccn($data['eccn'] ?? null);
        $document->setRdo($data['rdo'] ?? null);
        $document->setHts($data['hts'] ?? null);
        $document->setDocImpact(0);

        $existingPrincipalComment = null;
        foreach ($document->getCommentaires() as $comment) {
            if ($comment->gettype() === 'principal') {
                $existingPrincipalComment = $comment;
                break;
            }
        }

        if (isset($data['comments'])) {
            if ($existingPrincipalComment) {
                $existingPrincipalComment->setCommentaire($data['comments']);
            } else {
                $commentaire = new Commentaire();
                $commentaire->setUser($this->getUser());
                $commentaire->setCommentaire($data['comments']);
                $commentaire->settype('principal');
                $commentaire->setDocuments($document);
                $commentaire->setCreatedAt(new \DateTimeImmutable());
                $entityManager->persist($commentaire);
            }
        }

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Mise à jour du document');

        $entityManager->persist($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Document mis à jour avec succès']);
    }


    #[Route('/delete-document/{id}', name: 'delete_document', methods: ['POST'])]
    public function deleteDocument(Request $request, EntityManagerInterface $entityManager, $id): JsonResponse
    {
        $document = $entityManager->getRepository(Document::class)->find($id);

        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        foreach ($document->getCommentaires() as $comment) {
            $entityManager->remove($comment);
        }

        $entityManager->remove($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Document supprimé avec succès']);
    }

    // find document by id return json
    #[Route('/document/{id}', name: 'get_document', methods: ['POST'])]
    public function getDocument(Document $document): JsonResponse
    {
        // dd($document);
        return new JsonResponse($document->toJson());
    }

    #[Route('/document/count-document', name: 'count_document', methods: ['GET'])]
    public function countDocument(DocumentRepository $documentRepository): JsonResponse
    {
        // Utiliser la nouvelle méthode optimisée avec cache
        $count = $documentRepository->countDocumentsByWorkflowStepCached();
        return new JsonResponse($count);
    }

    #[Route('/document/export-csv/{place}', name: 'app_document_export_csv', methods: ['GET'])]
    public function exportDocumentsCsv(string $place, DocumentRepository $documentRepository): Response
    {
        // Redirection pour les places BE vers les packages
        if ($place === 'BE_0' || $place === 'BE_1' || $place === 'BE') {
            return new Response('Export non disponible pour cette place', 404);
        }

        // Utiliser la même logique que getAllDocumentIds pour récupérer les documents
        $conn = $documentRepository->getEntityManager()->getConnection();

        if ($place === 'Qual_Logistique') {
            $sql = "
                SELECT d.*
                FROM document d
                WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM visa v
                    WHERE v.released_drawing_id = d.id
                    AND v.name = 'visa_Qual_Logistique'
                    AND v.status = 'valid'
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql, ['$."' . $place . '"']);
        } else {
            $sql = "
                SELECT d.*
                FROM document d
                WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM visa v
                    WHERE v.released_drawing_id = d.id
                    AND v.name = ?
                    AND v.status = 'valid'
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql, ['$."' . $place . '"', 'visa_' . $place]);
        }

        $documentsData = $result->fetchAllAssociative();

        // Convertir en objets Document pour utiliser les getters
        $documents = [];
        foreach ($documentsData as $docData) {
            $document = $documentRepository->find($docData['id']);
            if ($document) {
                $documents[] = $document;
            }
        }

        // Générer le CSV
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="documents_' . $place . '_' . date('Y-m-d_H-i-s') . '.csv"');

        $csvData = "Pack;Activity;Reference;RefRev;ProdDraw;ProdDrawRev;DocType;ProcType;Material;MaterialType;Action;CLS;MOQ;Valorisation;CodePrix;GrpFraisGen;CurrentSteps\n";

        foreach ($documents as $document) {
            // Calculs SAP Data comme dans le template
            $class_val = '-';
            if ($document->getMatProdType() == 'FERT') {
                $class_val = '7920';
            } elseif ($document->getMatProdType() == 'HALB' && ($document->getProcType() == 'F' || $document->getProcType() == 'E')) {
                $class_val = '7900';
            } elseif ($document->getMatProdType() == 'HALB' && $document->getProcType() == 'F30') {
                $class_val = '7910';
            } elseif ($document->getMatProdType() == 'VERP') {
                $class_val = '3050';
            } elseif ($document->getMatProdType() == 'ROH') {
                if ($document->getDocType() == 'DOC') {
                    $class_val = '3000';
                } else {
                    $class_val = '3030';
                }
            }

            $code_prix = '-';
            if ($document->getProcType() == 'F' || $document->getProcType() == 'F30') {
                $code_prix = 'V';
            } elseif ($document->getProcType() == 'E') {
                $code_prix = 'S';
            }

            $frai_gen = '-';
            if ($document->getMatProdType() != 'FERT' && ($document->getProcType() == 'F' || $document->getProcType() == 'F30')) {
                $frai_gen = 'ROH';
            } elseif ($document->getMatProdType() != 'VERP' || $document->getMatProdType() != 'ROH') {
                $frai_gen = 'ROH';
            }

            $currentSteps = implode('|', array_keys($document->getCurrentSteps()));

            $csvData .= sprintf(
                "%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
                $document->getRelPack() ? $document->getRelPack()->getId() : '',
                $document->getRelPack() ? $document->getRelPack()->getActivity() : '',
                $document->getReference() ?? '',
                $document->getRefRev() ?? '',
                $document->getProdDraw() ?? '',
                $document->getProdDrawRev() ?? '',
                $document->getDocType() ?? '',
                $document->getProcType() ?? '',
                $document->getMaterial() ?? '',
                $document->getMaterialType() ?? '',
                $document->getAction() ?? '',
                $document->getCls() ?? '',
                $document->getMoq() ?? '',
                $class_val,
                $code_prix,
                $frai_gen,
                $currentSteps
            );
        }

        $response->setContent($csvData);
        return $response;
    }

    #[Route('/document/get-all-ids/{place}', name: 'app_document_get_all_ids', methods: ['GET'])]
    public function getAllDocumentIds(string $place, DocumentRepository $documentRepository): JsonResponse
    {
        // Redirection pour les places BE vers les packages
        if ($place === 'BE_0' || $place === 'BE_1' || $place === 'BE') {
            return new JsonResponse([]);
        }

        // Optimized: Get only IDs directly from database instead of loading full entities
        if ($place === 'Qual_Logistique' || $place === 'Logistique') {
            // Use native SQL to get only IDs for logistics steps
            $conn = $documentRepository->getEntityManager()->getConnection();
            $sql = "
                SELECT d.id
                FROM document d
                WHERE (
                    JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                    OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
                )
                AND NOT (
                    EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                    AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql);
            $documentIds = array_map(function($row) { return ['id' => (int)$row['id']]; }, $result->fetchAllAssociative());
        } else {
            // Use optimized query to get only IDs for other steps
            $conn = $documentRepository->getEntityManager()->getConnection();
            $sql = "
                SELECT d.id
                FROM document d
                WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM visa v
                    WHERE v.released_drawing_id = d.id
                    AND v.name = ?
                    AND v.status = 'valid'
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql, ['$."' . $place . '"', 'visa_' . $place]);
            $documentIds = array_map(function($row) { return ['id' => (int)$row['id']]; }, $result->fetchAllAssociative());
        }

        return new JsonResponse($documentIds);
    }

    #[Route('/document/package/{id}', name: 'get_document_package', methods: ['POST'])]
    public function getDocumentPackage(ReleasedPackage $package): JsonResponse
    {
        $documents = $package->getDocuments();
        $data = [];
        foreach ($documents as $document) {
            $data[] = $document->toJson();
        }
        return new JsonResponse($data);
    }



    #[Route('/teleportation', name: 'teleportation', methods: ['POST'])]
    public function teleportation(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = $request->request->all();
        $documentId = $data['document'];
        $position = $data['position'];
        $retourner = $data['retourner'];

        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['success' => false, 'error' => 'Document non trouvé'], 404);
        }

        // Supprimer les visas correspondants
        $visas = $document->getVisas();
        foreach ($retourner as $place) {
            if(in_array($place, ['Assembly','Machining','Molding'])){
                $place = "prod";
            }
            foreach ($visas as $visa) {
                if ($visa->getName() === 'visa_'.$place) {
                    $entityManager->remove($visa);
                }
            }
        }

        // Enregistrer la sortie de l'état actuel
        if (isset($document->getCurrentSteps()[$position])) {
            $document->addStateExit($position);
        }

        // Mettre à jour les états courants
        $currentSteps = $document->getCurrentSteps();
        unset($currentSteps[$position]);

        // Ajouter les nouveaux états et enregistrer les entrées
        foreach ($retourner as $place) {
            $currentSteps[$place] = 1;
            // Enregistrer l'entrée dans le nouvel état
            $document->addStateEnter($place);
        }

        $document->setCurrentSteps($currentSteps);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('state_change', $this->getUser(), 'Téléportation du document');

        $entityManager->flush();

        return new JsonResponse(['success' => true]);
    }


    #[Route('/comment-gid', name: 'comment_gid', methods: ['POST'])]
    public function commentGID(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = $request->request->all();
        $commentId = $data['commentId'];
        $documentId = $data['documentId'];
        $comment = $data['comment'];
        $state = $data['state'];

        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['success' => false, 'error' => 'Document non trouvé'], 404);
        }

        $commentaire = $entityManager->getRepository(Commentaire::class)->find($commentId);

        // If comment is empty and commentaire exists, delete it
        if (empty($comment) && $commentaire) {
            $entityManager->remove($commentaire);
            $entityManager->flush();
            return new JsonResponse(['success' => true]);
        }

        if (!$commentaire) {
            $commentaire = new Commentaire();
            $commentaire->setUser($this->getUser());
            $commentaire->setState($state);
            $commentaire->setType('secondaire');
            $commentaire->setDocuments($document);
            $commentaire->setCreatedAt(new \DateTimeImmutable());
        }

        $commentaire->setCommentaire($comment);
        $entityManager->persist($commentaire);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('comment', $this->getUser(), 'Ajout/modification d\'un commentaire ' . $state);

        $entityManager->flush();

        return new JsonResponse(['success' => true]);
    }
    #[Route('/update-modif-docImpact', name: 'update_modifdocImpact', methods: ['POST'])]
    public function updateModifExigencesQual(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if ($data === null) {
            return new JsonResponse(['status' => 'error', 'message' => 'Requête mal formée ou vide'], 400);
        }

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'], $data['docImpact'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];
        $docImpact = $data['docImpact'];

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // Mettre à jour le champ modifExigencesQual
        $document->setDocImpact($docImpact);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Modification de l\'impact documentaire');

        // Persister les modifications
        $entityManager->persist($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'docImpact mis à jour avec succès']);
    }

    //delete document ajax
    #[Route('/delete-document-ajax', name: 'delete_document_ajax', methods: ['POST'])]
    public function deleteDocumentAjax(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        if(!($this->getUser()->isManager())){
            return new JsonResponse(['status' => 'error', 'message' => 'Vous ne pouvez pas supprimer ce document'], 403);
        }
        $data = json_decode($request->getContent(), true);

        if ($data === null) {
            return new JsonResponse(['status' => 'error', 'message' => 'Requête mal formée ou vide'], 400);
        }

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // Supprimer les commentaires associés au document
        foreach ($document->getCommentaires() as $commentaire) {
            $entityManager->remove($commentaire);
        }

        // Supprimer le document
        $entityManager->remove($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Document et ses commentaires supprimés avec succès']);
    }

    #[Route('/find-document', name: 'find_document', methods: ['POST'])]
    public function findDocument(Request $request, DocumentRepository $documentRepository): JsonResponse
    {
        $reference = $request->request->get('reference');

        if (!$reference) {
            return new JsonResponse(['error' => 'Référence manquante'], 400);
        }

        // Rechercher le document avec la référence donnée et la révision la plus récente
        $documents = $documentRepository->findBy(['reference' => $reference], ['refRev' => 'DESC']);

        if (empty($documents)) {
            return new JsonResponse(['error' => 'Document non trouvé'], 404);
        }

        // Prendre le document avec la révision la plus récente (le premier dans la liste triée)
        $document = $documents[0];

        // Convertir le document en tableau pour le retourner en JSON
        $documentData = [
            'id' => $document->getId(),
            'reference' => $document->getReference(),
            'refRev' => $document->getRefRev(),
            'refTitleFra' => $document->getRefTitleFra(),
            'prodDraw' => $document->getProdDraw(),
            'prodDrawRev' => $document->getProdDrawRev(),
            'alias' => $document->getAlias(),
            'docType' => $document->getDocType(),
            'materialType' => $document->getMaterialType(),
            'matProdType' => $document->getMatProdType(),
            'procType' => $document->getProcType(),
            'inventoryImpact' => $document->getInventoryImpact(),
            'idAletiq' => $document->getIdAletiq(),
            'custDrawing' => $document->getCustDrawing(),
            'custDrawingRev' => $document->getCustDrawingRev(),
            'action' => $document->getAction(),
            'ex' => $document->getEx(),
            'weight' => $document->getWeight(),
            'weightUnit' => $document->getWeightUnit(),
            'material' => $document->getMaterial(),
            'platingSurface' => $document->getPlatingSurface(),
            'platingSurfaceUnit' => $document->getPlatingSurfaceUnit(),
            'internalMachRec' => $document->getInternalMachRec(),
            'cls' => $document->getCls(),
            'moq' => $document->getMoq(),
            'productCode' => $document->getProductCode(),
            'prodAgent' => $document->getProdAgent(),
            'mof' => $document->getMof(),
            'commodityCode' => $document->getCommodityCode(),
            'purchasingGroup' => $document->getPurchasingGroup(),
            'unit' => $document->getUnit(),
            'leadtime' => $document->getLeadtime(),
            'prisDans1' => $document->getPrisDans1(),
            'prisDans2' => $document->getPrisDans2(),
            'eccn' => $document->getEccn(),
            'rdo' => $document->getRdo(),
            'hts' => $document->getHts(),
            'fia' => $document->getFia(),
            'metroTime' => $document->getMetroTime(),
            'qInspection' => $document->getQInspection(),
            'qDynamization' => $document->getQDynamization(),
            'qDocRec' => $document->getQDocRec(),
            'qControlRouting' => $document->getQControlRouting(),
            'criticalComplete' => $document->getCriticalComplete(),
            'switchAletiq' => $document->getSwitchAletiq(),
            'metroControl' => $document->getMetroControl(),
            'docImpact' => $document->getDocImpact(),
        ];

        return new JsonResponse(['document' => $documentData]);
    }

    #[Route('/test-badges', name: 'test_badges', methods: ['GET'])]
    public function testBadges(DocumentRepository $documentRepository): JsonResponse
    {
        try {
            // Test direct du repository
            $badges = $documentRepository->countDocumentsByWorkflowStepCached();

            return new JsonResponse([
                'status' => 'success',
                'badges' => $badges,
                'count' => count($badges),
                'message' => 'Badges récupérés avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }
}
